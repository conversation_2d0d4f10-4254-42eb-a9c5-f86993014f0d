# Example: Users Table Implementation
# This shows how to use the admin table component for a users listing

# In app/helpers/admin/users_helper.rb
module Admin
  module UsersHelper
    def users_table_columns
      [
        table_column('Avatar', {
          content: ->(user) { 
            render 'layouts/shared/user_avatar', user: user, width: 32, height: 32
          },
          class: 'w-12'
        }),
        table_column('Name', {
          content: ->(user) { 
            content_tag(:div) do
              concat(content_tag(:div, user.full_name, class: 'font-medium text-gray-900'))
              concat(content_tag(:div, user.email, class: 'text-sm text-gray-500'))
            end
          }
        }),
        table_column('Role', {
          content: ->(user) { 
            content_tag(:span, user.role.humanize, 
                       class: 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800')
          }
        }),
        table_column('Practice', {
          content: ->(user) { user.practice&.name || 'No Practice' }
        }),
        table_column('Status', {
          content: ->(user) { user_status_badge(user) }
        }),
        table_column('Last Login', {
          content: ->(user) { 
            user.last_sign_in_at&.strftime("%d/%m/%Y %H:%M") || 'Never'
          },
          mobile_hidden: true
        })
      ]
    end

    def user_status_badge(user)
      if user.active?
        content_tag(:span, 'Active', 
                   class: 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800')
      else
        content_tag(:span, 'Inactive', 
                   class: 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800')
      end
    end

    def user_action_buttons(user)
      content_tag(:div, class: 'flex justify-end space-x-1.5 items-center') do
        concat(edit_action_button(edit_admin_user_path(user)))
        concat(action_button('Reset Password', reset_password_admin_user_path(user), {
          variant: 'secondary',
          type: 'button',
          'data-method': 'patch',
          'data-confirm': 'Send password reset email?'
        }))
        if user.active?
          concat(action_button('Deactivate', deactivate_admin_user_path(user), {
            variant: 'danger',
            type: 'button',
            'data-method': 'patch',
            'data-confirm': 'Deactivate this user?'
          }))
        else
          concat(action_button('Activate', activate_admin_user_path(user), {
            variant: 'success',
            type: 'button',
            'data-method': 'patch'
          }))
        end
        concat(view_action_button(admin_user_path(user)))
      end
    end
  end
end

# In app/views/admin/users/index.html.erb
# <div class="flex-1 overflow-auto p-4">
#   <div class="container mx-auto">
#     <%= admin_table(@users, users_table_columns, { 
#       empty_message: 'No users found',
#       table_class: 'w-full'
#     }) do |user| %>
#       <%= user_action_buttons(user) %>
#     <% end %>
#   </div>
# </div>

# Alternative: Simple attribute-based columns
def simple_users_table_columns
  [
    table_column('Name', { attribute: :full_name }),
    table_column('Email', { attribute: :email }),
    table_column('Role', { 
      content: ->(user) { user.role.humanize }
    }),
    table_column('Created', { 
      content: ->(user) { user.created_at.strftime("%d/%m/%Y") }
    })
  ]
end

# Example with custom styling
def styled_users_table
  admin_table(@users, users_table_columns, {
    row_class: 'group bg-gradient-to-r from-white to-gray-50 shadow-sm rounded-xl hover:shadow-lg transition-all duration-300 ease-in-out border border-gray-200/75 hover:border-blue-300',
    header_class: 'text-xs font-semibold text-blue-600 uppercase tracking-wider',
    cell_class: 'text-sm text-gray-700',
    empty_message: 'No team members found. Add your first user to get started!'
  }) do |user|
    user_action_buttons(user)
  end
end
