# Admin Table Component

A reusable table component for admin sections that provides semantic HTML table structure with Tailwind styling, responsive behavior, and customizable action buttons.

## Features

- **Semantic HTML**: Uses proper `<table>`, `<thead>`, `<tbody>`, `<tr>`, `<td>` elements
- **Responsive Design**: Grid layout for desktop, stacked cards for mobile
- **Hover Effects**: Smooth transitions and opacity changes for action buttons
- **Customizable Styling**: Configurable CSS classes for all elements
- **Action Buttons**: Built-in support for edit, delete, and view actions
- **Empty State**: Customizable empty message when no records exist
- **Accessibility**: Screen reader friendly with proper table structure

## Basic Usage

### Simple Table

```erb
<%= admin_table(@records, columns) do |record| %>
  <%= edit_action_button(edit_admin_record_path(record)) %>
  <%= delete_action_button %>
  <%= view_action_button(admin_record_path(record)) %>
<% end %>
```

### Define Columns

```ruby
def records_table_columns
  [
    table_column('Name', { attribute: :name }),
    table_column('Email', { attribute: :email }),
    table_column('Created', { 
      content: ->(record) { record.created_at.strftime("%d/%m/%Y") }
    }),
    table_column('Status', { 
      content: ->(record) { status_badge(record.status) },
      class: 'text-center'
    })
  ]
end
```

## Column Configuration

### Column Options

- `attribute`: Direct attribute access (e.g., `record.name`)
- `method`: Helper method to call with record as parameter
- `content`: Lambda/proc for custom content generation
- `class`: Additional CSS classes for the cell
- `mobile_hidden`: Hide column on mobile devices

### Examples

```ruby
# Simple attribute
table_column('Name', { attribute: :full_name })

# Helper method
table_column('Status', { method: :status_badge })

# Custom content with lambda
table_column('Actions', { 
  content: ->(record) { 
    link_to 'View', record_path(record), class: 'btn btn-primary'
  }
})

# Mobile hidden column
table_column('Internal ID', { 
  attribute: :id, 
  mobile_hidden: true 
})
```

## Action Buttons

### Built-in Action Buttons

```ruby
# Edit button
edit_action_button(edit_path)

# Delete button (renders as button, not link)
delete_action_button

# Delete with custom options
delete_action_button({ 
  'data-confirm': 'Are you sure?',
  'data-method': 'delete'
})

# View button (chevron icon)
view_action_button(show_path)

# Custom action button
action_button('Approve', approve_path, {
  variant: 'success',
  icon: 'check'
})
```

### Action Button Variants

- `primary`: Blue color scheme
- `secondary`: Gray color scheme (default)
- `danger`: Red color scheme
- `success`: Green color scheme

## Table Options

### Available Options

```ruby
options = {
  table_class: 'w-full',                    # Table CSS classes
  row_class: 'group bg-white...',           # Row CSS classes
  header_class: 'text-xs font-normal...',   # Header CSS classes
  cell_class: 'text-sm text-gray-600',      # Cell CSS classes
  actions_class: 'flex justify-end...',     # Actions column CSS classes
  empty_message: 'No records found',        # Empty state message
  show_actions: true,                       # Show/hide actions column
  responsive: true                          # Enable responsive behavior
}

admin_table(@records, columns, options) do |record|
  # action buttons
end
```

## Real-world Example: Patients Table

```ruby
# In helper (app/helpers/application_helper.rb)
def patients_table_columns
  [
    table_column('Name', {
      content: ->(patient) { 
        content_tag(:div, class: 'flex flex-col md:flex-row md:items-center') do
          content_tag(:span, class: 'text-sm font-medium text-gray-800 group-hover:text-gray-900') do
            link_to admin_patient_path(patient) do
              "#{patient.first_name} #{patient.last_name}"
            end
          end
        end
      }
    }),
    table_column('Date of Birth', {
      content: ->(patient) { patient.date_of_birth&.strftime("%d/%m/%Y") || "--/--/----" }
    }),
    table_column('Email', {
      content: ->(patient) { patient.email || "No Email" }
    }),
    table_column('Mobile Phone', {
      content: ->(patient) { patient.mobile_phone || "No Phone" }
    }),
    table_column('Postcode', {
      content: ->(patient) { patient.postcode || "No Postcode" }
    }),
    table_column('Practices', {
      content: ->(patient) { patient.practices.map(&:name).join(", ") }
    })
  ]
end

def patient_action_buttons(patient, archived: false)
  content_tag(:div, class: 'mt-2 md:mt-0 flex flex-col space-y-2 md:space-y-0 md:flex-row md:justify-end md:space-x-1.5 items-stretch md:items-center') do
    concat(edit_action_button(edit_admin_patient_path(patient)))
    concat(delete_action_button) unless archived
    concat(view_action_button(admin_patient_path(patient)))
  end
end
```

```erb
<!-- In view (app/views/admin/patients/index.html.erb) -->
<%= admin_table(@patients, patients_table_columns, { empty_message: 'No patients found' }) do |patient| %>
  <%= patient_action_buttons(patient, archived: params[:archived] == 'true') %>
<% end %>
```

## Responsive Behavior

- **Desktop**: Grid layout with proper table structure
- **Mobile**: Stacked card layout with label-value pairs
- **Action buttons**: Hidden by default, shown on hover (desktop) or always visible (mobile)

## Styling Integration

The component integrates with the existing `app/javascript/stylesheets/admin/shared/table.scss` file and uses Tailwind classes for consistent styling across the application.

## Files Structure

- `app/helpers/admin/table_helper.rb` - Main table helper methods
- `app/views/shared/_admin_table.html.erb` - Table partial template
- `app/helpers/application_helper.rb` - Convenience methods and patient-specific helpers
- `app/javascript/stylesheets/admin/shared/table.scss` - Table styling (existing)
