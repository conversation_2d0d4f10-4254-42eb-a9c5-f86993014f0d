# frozen_string_literal: true

module Application<PERSON><PERSON>per
  def page_title
    fallback = content_for(:title) || controller_name.humanize.titleize
    site = Current.practice&.name || 'UPOD Medical'

    # rubocop:disable Rails/HelperInstanceVariable
    [@page_title || fallback, site].compact.join(' – ')
    # rubocop:enable Rails/HelperInstanceVariable
  end

  def number_of_results(count)
    content_tag :span, pluralize(count, 'result')
  end

  def wicked_step
    controller.steps.index(controller.step) + 1 if controller.respond_to?(:steps) && controller.respond_to?(:step)
  end

  include Admin::TableHelper

  def admin_table(collection, columns, options = {}, &block)
    admin_data_table(collection, columns, options, &block)
  end

  def patients_table_columns
    [
      table_column('Name', {
        content: ->(patient) {
          content_tag(:div, class: 'flex flex-col md:flex-row md:items-center') do
            content_tag(:span, class: 'text-sm font-medium text-gray-800 group-hover:text-gray-900') do
              link_to admin_patient_path(patient) do
                "#{patient.first_name} #{patient.last_name}"
              end
            end
          end
        }
      }),
      table_column('Date of Birth', {
        content: ->(patient) { patient.date_of_birth&.strftime("%d/%m/%Y") || "--/--/----" }
      }),
      table_column('Email', {
        content: ->(patient) { patient.email || "No Email" }
      }),
      table_column('Mobile Phone', {
        content: ->(patient) { patient.mobile_phone || "No Phone" }
      }),
      table_column('Postcode', {
        content: ->(patient) { patient.postcode || "No Postcode" }
      }),
      table_column('Practices', {
        content: ->(patient) { patient.practices.map(&:name).join(", ") }
      })
    ]
  end

  def patient_action_buttons(patient, archived: false)
    content_tag(:div, class: 'mt-2 md:mt-0 flex flex-col space-y-2 md:space-y-0 md:flex-row md:justify-end md:space-x-1.5 items-stretch md:items-center') do
      concat(edit_action_button(edit_admin_patient_path(patient)))
      concat(delete_action_button) unless archived
      concat(view_action_button(admin_patient_path(patient)))
    end
  end



  RGB_REGEXP = /\A#(..)(..)(..)\z/
  VALID_FORMAT = /\A#[a-fA-F0-9]{6}\z/

  def get_text_color(hex)
    return '#000000' unless hex =~ VALID_FORMAT

    red, green, blue = hex.match(RGB_REGEXP).captures.map(&:hex)

    (red * 0.299 + green * 0.587 + blue * 0.114) > 186 ? '#000000' : '#FFFFFF'
  end

  def get_inverted_color(hex)
    return '#000000' unless hex =~ VALID_FORMAT

    red, green, blue = hex.match(RGB_REGEXP).captures.map(&:hex)

    (red * 0.299 + green * 0.587 + blue * 0.114) > 186 ? '#FFFFFF' : '#000000'
  end

  # Renders a practice logo or initial in a circle
  # @param practice [Practice] the practice object
  # @param size_class [String] CSS classes for the container size
  # @param additional_classes [String] additional CSS classes for the container
  # @return [String] HTML for the practice logo or initial
  def practice_logo_or_initial(practice, size_class = 'w-6 h-6', additional_classes = '')
    return '' unless practice

    container_classes = "rounded-full overflow-hidden border border-gray-200 flex-shrink-0 #{size_class} #{additional_classes}"

    if practice.logo.attached?
      content_tag :div, class: container_classes do
        image_tag practice.logo, alt: practice.name, class: 'w-full h-full object-cover'
      end
    else
      content_tag :div, class: container_classes do
        content_tag :div, class: 'w-full h-full bg-blue-100 flex items-center justify-center text-blue-600 font-medium' do
          practice.name&.first&.upcase || '?'
        end
      end
    end
  end
end
