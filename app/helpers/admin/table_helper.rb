# frozen_string_literal: true

module Admin
  module TableHelper
    def admin_data_table(collection, columns, options = {}, &block)
      defaults = {
        table_class: 'w-full',
        row_class: 'group bg-white shadow-sm rounded-xl hover:shadow-lg transition-all duration-300 ease-in-out border border-gray-200/75 hover:border-gray-300',
        header_class: 'text-xs font-normal text-gray-400 uppercase tracking-wider',
        cell_class: 'text-sm text-gray-600',
        actions_class: 'flex justify-end space-x-1.5 items-center',
        empty_message: 'No records found',
        show_actions: true,
        responsive: true
      }
      
      options = defaults.merge(options)
      
      render partial: 'shared/admin_table', locals: {
        collection: collection,
        columns: columns,
        options: options,
        block: block
      }
    end

    def table_column(label, options = {})
      {
        label: label,
        attribute: options[:attribute],
        method: options[:method],
        content: options[:content],
        class: options[:class],
        mobile_hidden: options[:mobile_hidden] || false
      }
    end

    def action_button(text, path_or_options = {}, options = {})
      if path_or_options.is_a?(Hash)
        options = path_or_options
        path_or_options = '#'
      end

      defaults = {
        class: 'inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 rounded-md text-xs h-auto px-2.5 py-1.5 group-hover:opacity-100 md:opacity-0 transition-opacity duration-200',
        icon: nil,
        variant: 'secondary'
      }

      options = defaults.merge(options)

      variant_classes = {
        'primary' => 'text-blue-600 hover:text-blue-700 hover:bg-blue-50',
        'secondary' => 'text-gray-500 hover:text-gray-700 hover:bg-gray-50',
        'danger' => 'text-red-500 hover:text-red-700 hover:bg-red-50',
        'success' => 'text-green-600 hover:text-green-700 hover:bg-green-50'
      }

      css_classes = [options[:class], variant_classes[options[:variant]]].compact.join(' ')

      if options[:type] == 'button'
        content_tag(:button, class: css_classes, **options.except(:class, :icon, :variant, :type)) do
          concat(render_icon(options[:icon])) if options[:icon]
          concat(content_tag(:span, text, class: 'ml-1.5 md:hidden lg:inline'))
        end
      else
        link_to path_or_options, class: css_classes, **options.except(:class, :icon, :variant, :type) do
          concat(render_icon(options[:icon])) if options[:icon]
          concat(content_tag(:span, text, class: 'ml-1.5 md:hidden lg:inline'))
        end
      end
    end

    def edit_action_button(path, options = {})
      action_button('Edit', path, {
        variant: 'primary',
        icon: 'edit'
      }.merge(options))
    end

    def delete_action_button(path_or_options = {}, options = {})
      if path_or_options.is_a?(Hash)
        options = path_or_options
        path_or_options = '#'
      end

      action_button('Delete', path_or_options, {
        variant: 'danger',
        icon: 'trash',
        type: 'button'
      }.merge(options))
    end

    def view_action_button(path, options = {})
      link_to path, class: 'inline-block' do
        render_icon('chevron-right', 'h-4 w-4 text-gray-300 group-hover:text-gray-400 md:group-hover:opacity-0 transition-opacity duration-200')
      end
    end

    private

    def render_icon(icon_name, additional_classes = 'h-3.5 w-3.5')
      case icon_name
      when 'edit'
        content_tag(:svg, xmlns: 'http://www.w3.org/2000/svg', width: '24', height: '24', viewBox: '0 0 24 24', fill: 'none', stroke: 'currentColor', 'stroke-width': '2', 'stroke-linecap': 'round', 'stroke-linejoin': 'round', class: "lucide lucide-pen-line #{additional_classes}") do
          concat(content_tag(:path, '', d: 'M12 20h9'))
          concat(content_tag(:path, '', d: 'M16.5 3.5a2.12 2.12 0 0 1 3 3L7 19l-4 1 1-4Z'))
        end
      when 'trash'
        content_tag(:svg, xmlns: 'http://www.w3.org/2000/svg', width: '24', height: '24', viewBox: '0 0 24 24', fill: 'none', stroke: 'currentColor', 'stroke-width': '2', 'stroke-linecap': 'round', 'stroke-linejoin': 'round', class: "lucide lucide-trash2 #{additional_classes}") do
          concat(content_tag(:path, '', d: 'M3 6h18'))
          concat(content_tag(:path, '', d: 'M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6'))
          concat(content_tag(:path, '', d: 'M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2'))
          concat(content_tag(:line, '', x1: '10', x2: '10', y1: '11', y2: '17'))
          concat(content_tag(:line, '', x1: '14', x2: '14', y1: '11', y2: '17'))
        end
      when 'chevron-right'
        content_tag(:svg, xmlns: 'http://www.w3.org/2000/svg', width: '24', height: '24', viewBox: '0 0 24 24', fill: 'none', stroke: 'currentColor', 'stroke-width': '2', 'stroke-linecap': 'round', 'stroke-linejoin': 'round', class: "lucide lucide-chevron-right #{additional_classes}") do
          content_tag(:path, '', d: 'm9 18 6-6-6-6')
        end
      end
    end
  end
end
