<div class="space-y-3">
  <div class="hidden md:block">
    <div class="grid grid-cols-<%= columns.length + (options[:show_actions] ? 1 : 0) %> items-center gap-x-6 px-5 py-2 mb-3">
      <% columns.each do |column| %>
        <div class="<%= options[:header_class] %>">
          <%= column[:label] %>
        </div>
      <% end %>
      <% if options[:show_actions] %>
        <div class="<%= options[:header_class] %> text-right sr-only">Actions</div>
      <% end %>
    </div>

    <% if collection.empty? %>
      <div class="bg-white p-8 rounded-lg shadow text-center">
        <p class="text-gray-500 text-lg"><%= options[:empty_message] %></p>
      </div>
    <% else %>
      <table class="<%= options[:table_class] %>">
        <thead class="sr-only">
          <tr>
            <% columns.each do |column| %>
              <th><%= column[:label] %></th>
            <% end %>
            <% if options[:show_actions] %>
              <th>Actions</th>
            <% end %>
          </tr>
        </thead>
        <tbody class="space-y-3">
          <% collection.each do |record| %>
            <tr class="<%= options[:row_class] %> grid grid-cols-<%= columns.length + (options[:show_actions] ? 1 : 0) %> items-center gap-x-6 p-4">
              <% columns.each do |column| %>
                <td class="<%= options[:cell_class] %> <%= column[:class] if column[:class] %>">
                  <% if column[:content] %>
                    <%= capture { column[:content].call(record) } %>
                  <% elsif column[:attribute] %>
                    <%= record.send(column[:attribute]) %>
                  <% elsif column[:method] %>
                    <%= send(column[:method], record) %>
                  <% end %>
                </td>
              <% end %>
              <% if options[:show_actions] %>
                <td class="<%= options[:actions_class] %>">
                  <% if block_given? %>
                    <%= capture(record, &block) %>
                  <% end %>
                </td>
              <% end %>
            </tr>
          <% end %>
        </tbody>
      </table>
    <% end %>
  </div>

  <div class="md:hidden space-y-3">
    <% if collection.empty? %>
      <div class="bg-white p-8 rounded-lg shadow text-center">
        <p class="text-gray-500 text-lg"><%= options[:empty_message] %></p>
      </div>
    <% else %>
      <% collection.each do |record| %>
        <div class="<%= options[:row_class] %> p-4">
          <% columns.each do |column| %>
            <% next if column[:mobile_hidden] %>
            <div class="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
              <span class="<%= options[:header_class] %>"><%= column[:label] %></span>
              <span class="<%= options[:cell_class] %> <%= column[:class] if column[:class] %>">
                <% if column[:content] %>
                  <%= capture { column[:content].call(record) } %>
                <% elsif column[:attribute] %>
                  <%= record.send(column[:attribute]) %>
                <% elsif column[:method] %>
                  <%= send(column[:method], record) %>
                <% end %>
              </span>
            </div>
          <% end %>
          <% if options[:show_actions] && block_given? %>
            <div class="mt-4 pt-4 border-t border-gray-100">
              <div class="<%= options[:actions_class] %>">
                <%= capture(record, &block) %>
              </div>
            </div>
          <% end %>
        </div>
      <% end %>
    <% end %>
  </div>
</div>
