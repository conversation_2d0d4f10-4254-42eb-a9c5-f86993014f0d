<div class="flex flex-col h-screen bg-[#f5f5f7]">
  <div class="flex-shrink-0 bg-white border-b border-gray-200">
    <div class="container mx-auto px-4 py-4">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <h1 class="text-xl font-semibold">Patient List</h1>
          <div class="ml-4">
            <div class="flex items-center gap-2 bg-gray-50/90 p-1.5 rounded-full backdrop-blur-md border border-gray-200/60 shadow-sm">
              <a href="<%= admin_patients_path(archived: false) %>"
                 class="filter-btn min-w-[36px] <%= params[:archived] != 'true' ? 'pl-3 pr-4' : 'px-3' %> h-9 rounded-full transition-all duration-300 ease-in-out flex items-center justify-center shadow-sm hover:shadow-md border border-gray-200/40 overflow-hidden <%= params[:archived] != 'true' ? 'text-blue-800' : 'bg-white text-gray-600 hover:bg-gray-50' %>"
                 <%= params[:archived] != 'true' ? 'style="background: linear-gradient(to right, #bfdbfe, #93c5fd);"'.html_safe : '' %>>
                <i class="fa-light fa-user h-4 w-4 <%= params[:archived] != 'true' ? 'text-blue-700' : '' %>"></i>
                <span class="tab-text text-xs font-medium tracking-wide <%= params[:archived] != 'true' ? 'ml-1.5' : 'opacity-0 w-0' %> transition-all duration-300">Active</span>
              </a>
              <a href="<%= admin_patients_path(archived: true) %>"
                 class="filter-btn min-w-[36px] <%= params[:archived] == 'true' ? 'pl-3 pr-4' : 'px-3' %> h-9 rounded-full transition-all duration-300 ease-in-out flex items-center justify-center shadow-sm hover:shadow-md border border-gray-200/40 overflow-hidden <%= params[:archived] == 'true' ? 'text-red-800' : 'bg-white text-gray-600 hover:bg-gray-50' %>"
                 <%= params[:archived] == 'true' ? 'style="background: linear-gradient(to right, #fecaca, #fca5a5);"'.html_safe : '' %>>
                <i class="fa-light fa-archive h-4 w-4 <%= params[:archived] == 'true' ? 'text-red-700' : '' %>"></i>
                <span class="tab-text text-xs font-medium tracking-wide <%= params[:archived] == 'true' ? 'ml-1.5' : 'opacity-0 w-0' %> transition-all duration-300">Archived</span>
              </a>
            </div>
          </div>
        </div>
        <div class="flex items-center">
          <%= form_tag create_temporary_admin_patients_path, method: :post, class: "inline-block" do %>
            <button type="submit" class="inline-flex items-center justify-center gap-2 whitespace-nowrap ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border hover:text-accent-foreground py-2 h-10 px-4 bg-blue-100 hover:bg-blue-200 text-blue-800 border-blue-200 rounded-lg shadow-sm transition-all duration-200 text-xs font-medium">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-user-plus w-4 h-4 mr-1">
                <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
                <circle cx="9" cy="7" r="4"></circle>
                <line x1="19" x2="19" y1="8" y2="14"></line>
                <line x1="22" x2="16" y1="11" y2="11"></line>
              </svg>
              Add Patient
            </button>
          <% end %>
        </div>
      </div>
    </div>
  </div>

  <div class="flex-1 overflow-auto p-4">
    <div class="container mx-auto">
      <%= admin_table(@patients, patients_table_columns, { empty_message: 'No patients found' }) do |patient| %>
        <%= patient_action_buttons(patient, archived: params[:archived] == 'true') %>
      <% end %>

      <% unless @patients.empty? %>
        <div class="mt-4 flex justify-between items-center bg-white p-4 rounded-xl shadow-sm border border-gray-200/75">
          <div class="text-sm text-gray-600">
            Showing <%= @patients.offset_value + 1 %> to <%= [@patients.offset_value + @patients.length, @patients.total_entries].min %> of <%= @patients.total_entries %> patients
          </div>

          <div class="pagination-container">
            <%= will_paginate @patients,
              class: "flex items-center space-x-2",
              inner_window: 1,
              outer_window: 0,
              previous_label: "Previous",
              next_label: "Next",
              page_links: true,
              link_options: { class: "inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 border-gray-200" },
              params: params.permit(:page, :search) %>
          </div>
        </div>
      <% end %>
    </div>
  </div>
</div>
